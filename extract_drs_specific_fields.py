import asyncio
import re
import json
import csv
import os
from datetime import datetime
from crawl4ai import AsyncWebCrawler
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy

def get_team_names(url):
    """
    Extract team names from URL
    URL format: .../team1-vs-team2-4th-match-1473441/...
    """
    match_segment = url.split('/')[-2]
    teams_part = match_segment.split('-vs-')
    if len(teams_part) >= 2:
        team1 = teams_part[0].replace('-', ' ').title()
        team2 = teams_part[1].split('-')[0].replace('-', ' ').title()
        return team1, team2
    return None, None

def get_team_abbreviation(team_name):
    """
    Get standard abbreviation for IPL team names
    """
    team_abbr = {
        'Royal Challengers Bengaluru': '<PERSON><PERSON>',
        'Royal Challengers Bangalore': 'RCB',
        'Royal Challengers': 'RCB',
        'Chennai Super Kings': 'CSK',
        'Chennai': 'CSK',
        'Delhi Capitals': 'DC',
        'Delhi': 'DC',
        'Gujarat Titans': 'GT',
        'Gujarat': 'GT',
        'Kolkata Knight Riders': 'KKR',
        'Kolkata': 'KKR',
        'Lucknow Super Giants': 'LSG',
        'Lucknow': 'LSG',
        'Mumbai Indians': 'MI',
        'Mumbai': 'MI',
        'Punjab Kings': 'PBKS',
        'Punjab': 'PBKS',
        'Kings XI Punjab': 'PBKS',
        'Kings XI': 'PBKS',
        'Rajasthan Royals': 'RR',
        'Rajasthan': 'RR',
        'Sunrisers Hyderabad': 'SRH',
        'Sunrisers': 'SRH'
    }
    return team_abbr.get(team_name, team_name[:3].upper())

async def extract_drs_data(crawler, match_url):
    """
    Extract specific DRS review fields from a match URL using an existing crawler
    """
    print(f"Processing match: {match_url}")

    # Extract match ID from URL
    match_id = match_url.split('/')[-2].split('-')[-1]

    # Extract team names and create a formatted teams string
    team1, team2 = get_team_names(match_url)
    teams_str = "Unknown vs Unknown"

    if team1 and team2:
        team1_abbr = get_team_abbreviation(team1)
        team2_abbr = get_team_abbreviation(team2)
        teams_str = f"{team1_abbr} vs {team2_abbr}"

    print(f"Teams: {teams_str}")

    # Configure the crawler run
    md_generator = DefaultMarkdownGenerator(
        options={
            "ignore_links": True,
            "escape_html": False,
            "body_width": 0
        }
    )

    run_config = CrawlerRunConfig(
        exclude_external_links=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=md_generator,
        cache_mode=CacheMode.DISABLED  # Disable cache to get fresh data
    )

    # List to store all DRS reviews for CSV export
    all_reviews = []

    try:
        # Use the existing crawler to fetch the page
        result = await crawler.arun(url=match_url, config=run_config)

        if not result.success:
            print(f"Failed to fetch page: {result.error_message}")
            return all_reviews

        # Get the HTML content
        html_content = None

        # Try different attributes to get the HTML content
        if hasattr(result, 'raw_content'):
            html_content = result.raw_content
        elif hasattr(result, 'html'):
            html_content = result.html
        elif hasattr(result, 'response') and hasattr(result.response, 'text'):
            html_content = result.response.text

        if not html_content:
            print("Could not extract HTML content from the page")
            return all_reviews

        # Find all occurrences of text between "inningDRSReviews" and "inningOverGroups"
        pattern = r'"inningDRSReviews":(.*?),"inningOverGroups"'
        matches = re.findall(pattern, html_content, re.DOTALL)

        if not matches:
            print("No matches found for the pattern")
            return all_reviews

        print(f"Found {len(matches)} innings with DRS reviews")

        # Process each innings
        for innings_idx, match in enumerate(matches, 1):
            try:
                # Clean up the JSON string
                json_str = match.strip()

                # Parse the JSON data
                reviews = json.loads(json_str)

                # Comment out print statements to make output cleaner
                print(f"\nInnings {innings_idx}: Found {len(reviews)} DRS reviews")

                # # Print header for the table
                print("\n{:<5} {:<15} {:<10} {:<10} {:<10} {:<10} {:<15} {:<15} {:<30} {:<30} {:<30} {:<15} {:<15} {:<15} {:<20}".format(
                    "No.", "Teams", "Over", "Innings", "Type", "SubType", "Team", "ReviewSide", "Umpire", "Batsman", "Bowler",
                    "Original", "DRS", "ResultType", "Review Result"
                ))
                print("-" * 220)

                # Process each review
                for review_idx, review in enumerate(reviews, 1):
                    # Extract the specific fields
                    decision_type = review.get('decisionChallengeType', '')
                    decision_subtype = review.get('decisionChallengeSubType', '')

                    # Extract team name from reviewTeam dictionary
                    review_team = review.get('reviewTeam', {})
                    team_name = review_team.get('name', '')

                    # Extract umpire longName from umpire dictionary
                    umpire = review.get('umpire', {})
                    umpire_name = umpire.get('longName', '')

                    # Extract batsman longName from batsman dictionary
                    batsman = review.get('batsman', {})
                    batsman_name = batsman.get('longName', '')

                    # Extract bowler longName from bowler dictionary
                    bowler = review.get('bowler', {})
                    bowler_name = bowler.get('longName', '')

                    # Extract over information
                    over_num = review.get('oversActual', '')

                    # Extract decision information
                    original_decision = review.get('originalDecision', '')
                    drs_decision = review.get('drsDecision', '')

                    # Extract additional fields
                    review_side = review.get('reviewSide', '')
                    result_type = review.get('resultType', '')
                    is_umpire_call = review.get('isUmpireCall', False)

                    # Determine Review Result based on the new logic
                    review_result = ""
                    if result_type == "upheld":
                        review_result = "Successful"
                    elif result_type == "struck" and is_umpire_call:
                        review_result = "Unsuccessful (Umpire's Call)"
                    elif result_type == "struck":
                        review_result = "Unsuccessful"
                    else:
                        # Fallback to the original logic if result_type is not recognized
                        if original_decision == "notcalled" and drs_decision == "called":
                            review_result = "Successful"
                        elif original_decision == "called" and drs_decision == "notcalled":
                            review_result = "Successful"
                        elif original_decision == "called" and drs_decision == "called":
                            review_result = "Unsuccessful"
                        elif original_decision == "notcalled" and drs_decision == "notcalled":
                            review_result = "Unsuccessful"

                    # Create a dictionary for this review
                    review_data = {
                        'Match ID': match_id,
                        'Teams': teams_str,
                        'Review Number': review_idx,
                        'Over': over_num,
                        'Innings': innings_idx,
                        'Decision Type': decision_type,
                        'Decision SubType': decision_subtype,
                        'Team': team_name,
                        'Review Side': review_side,
                        'Umpire': umpire_name,
                        'Batsman': batsman_name,
                        'Bowler': bowler_name,
                        'Original Decision': original_decision,
                        'DRS Decision': drs_decision,
                        'Result Type': result_type,
                        'Review Result': review_result
                    }

                    # Add to the list of all reviews
                    all_reviews.append(review_data)

                    # Comment out row printing to make output cleaner
                    print("{:<5} {:<15} {:<10} {:<10} {:<10} {:<10} {:<15} {:<15} {:<30} {:<30} {:<30} {:<15} {:<15} {:<15} {:<20}".format(
                        review_idx, teams_str, over_num, innings_idx, decision_type, decision_subtype, team_name, review_side, umpire_name,
                        batsman_name, bowler_name, original_decision, drs_decision, result_type, review_result
                    ))

                # print("-" * 220)

            except Exception as e:
                print(f"Error processing innings {innings_idx}: {str(e)}")

    except Exception as e:
        print(f"Error processing match: {str(e)}")

    return all_reviews

async def get_series_matches(crawler, run_config, test_mode=False):
    """
    Get match URLs for the series.

    Args:
        crawler: The web crawler instance
        run_config: The crawler run configuration
        test_mode: If True, returns only the first match for testing. If False, returns all matches.

    Returns:
        A list of match URLs
    """
    # You can change the series ID to process different IPL seasons
    series_id = "ipl-2025-1449924"
    #series_id = "indian-premier-league-2024-1410320"
    #series_id = "indian-premier-league-2023-1345038"
    #series_id = "indian-premier-league-2022-1298423"
    #series_id = "ipl-2021-1249214"
    #series_id = "ipl-2020-21-1210595"
    #series_id = "ipl-2019-1165643"

    # Extract season from series_id
    season = ""
    if "ipl-" in series_id:
        # Format: ipl-2025-1449924
        season = series_id.split("-")[1].split("-")[0]
    elif "indian-premier-league-" in series_id:
        # Format: indian-premier-league-2022-1298423
        season = series_id.split("-")[3].split("-")[0]
    else:
        # Default to the first 4-digit number found
        match = re.search(r'20\d\d', series_id)
        if match:
            season = match.group(0)

    # For testing with a single match, you can uncomment and use this direct URL
    if test_mode:
        print("\nRunning in TEST MODE - processing only one match")
        # Use a match that has DRS reviews for testing
        #test_match_url = "https://www.espncricinfo.com/series/ipl-2025-1449924/kolkata-knight-riders-vs-chennai-super-kings-57th-match-1473494/full-scorecard"
        test_match_url = "https://www.espncricinfo.com/series/indian-premier-league-2022-1298423/chennai-super-kings-vs-kolkata-knight-riders-1st-match-1304047/full-scorecard"
        return [test_match_url]

    # For full series processing
    urls_to_check = [
        f"https://www.espncricinfo.com/series/{series_id}/match-schedule-fixtures-and-results",
        f"https://www.espncricinfo.com/series/{series_id}/matches"
    ]

    match_urls = set()  # Using set to avoid duplicates

    for series_url in urls_to_check:
        result = await crawler.arun(url=series_url, config=run_config)

        if result.success:
            # Filter links that contain both the series ID and end with full-scorecard
            new_urls = [
                link['href'] for link in result.links["internal"]
                if series_id in link['href'] and 'full-scorecard' in link['href']
            ]
            match_urls.update(new_urls)  # Add new URLs to set
        else:
            print(f"Failed to fetch page {series_url}: {result.error_message}")

    # Convert set to list and sort based on match number in URL
    match_urls = list(match_urls)

    def get_match_number(url):
        # Extract match number from URL format: .../team1-vs-team2-4th-match-1473441/...
        try:
            parts = url.split('/')[-2].split('-')
            for i, part in enumerate(parts):
                if part == 'match':
                    match_num = parts[i-1].rstrip('thndrdst')  # Remove ordinal suffixes
                    return int(match_num)
        except:
            return float('inf')  # Put URLs with unexpected format at the end
        return float('inf')

    match_urls.sort(key=get_match_number)

    print(f"\nFound {len(match_urls)} match URLs in chronological order:")
    for url in match_urls[:5]:  # Show first 5 matches
        match_num = get_match_number(url)
        print(f"Match #{match_num}: {url}")

    if len(match_urls) > 5:
        print(f"... and {len(match_urls) - 5} more matches")

    print(f"Season identified: {season}")

    return match_urls, season

async def main():
    # Set to True for testing with a single match, False for processing all matches
    TEST_MODE = True

    # Create output directory if it doesn't exist
    os.makedirs('output', exist_ok=True)

    # Generate timestamp for the filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Configure the browser
    browser_config = BrowserConfig(verbose=True)

    # Configure the crawler run
    md_generator = DefaultMarkdownGenerator(
        options={
            "ignore_links": True,
            "escape_html": False,
            "body_width": 0
        }
    )

    run_config = CrawlerRunConfig(
        exclude_external_links=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=md_generator,
        cache_mode=CacheMode.DISABLED  # Disable cache to get fresh data
    )

    # Create CSV filename based on mode
    if TEST_MODE:
        csv_filename = f"output/drs_reviews_test_{timestamp}.csv"
    else:
        # We'll get the season from the match_urls later
        csv_filename = f"output/ipl_drs_reviews_{timestamp}.csv"  # Temporary name, will be updated later

    # Process all matches using a single browser instance
    all_reviews = []

    # Create a single crawler instance for all operations
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Get match URLs for the series
        print("Fetching match URLs...")
        match_urls, season = await get_series_matches(crawler, run_config, test_mode=TEST_MODE)

        if not match_urls:
            print("No match URLs found. Exiting...")
            return

        # Update the CSV filename with the season
        if not TEST_MODE:
            csv_filename = f"output/ipl_{season}_drs_reviews_{timestamp}.csv"
            print(f"CSV will be saved as: {csv_filename}")

        # Process each match
        for i, match_url in enumerate(match_urls, 1):
            print(f"\nProcessing match {i}/{len(match_urls)}")

            # Extract DRS data for this match using the shared crawler
            match_reviews = await extract_drs_data(crawler, match_url)

            # Add season to each review
            for review in match_reviews:
                review['Season'] = season

            all_reviews.extend(match_reviews)

            # Add a small delay between requests to avoid rate limiting
            if i < len(match_urls):
                print("Waiting 3 seconds before processing next match...")
                await asyncio.sleep(3)

    # Write all reviews to CSV file
    if all_reviews:
        # Write data to CSV file
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            # Define the fieldnames (column headers)
            fieldnames = [
                'Match ID', 'Teams', 'Review Number', 'Over', 'Innings', 'Decision Type', 'Decision SubType',
                'Team', 'Review Side', 'Umpire', 'Batsman', 'Bowler', 'Original Decision',
                'DRS Decision', 'Result Type', 'Review Result', 'Season'
            ]

            # Create CSV writer
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write the header row
            writer.writeheader()

            # Write all reviews
            writer.writerows(all_reviews)

        print(f"\nCSV file created: {csv_filename}")
        print(f"Total DRS reviews exported: {len(all_reviews)}")
    else:
        print("No DRS data was collected to export")

if __name__ == "__main__":
    asyncio.run(main())
