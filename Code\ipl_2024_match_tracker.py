import asyncio
from crawl4ai import Async<PERSON>eb<PERSON>raw<PERSON>
from crawl4ai.async_configs import <PERSON><PERSON>erConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from crawl4ai.markdown_generation_strategy import <PERSON><PERSON>ultM<PERSON><PERSON>Generator

async def get_series_matches(crawler, run_config):
    series_id = "indian-premier-league-2024-1410320"
    urls_to_check = [
        f"https://www.espncricinfo.com/series/{series_id}/match-schedule-fixtures-and-results",
        f"https://www.espncricinfo.com/series/{series_id}/matches"
    ]
    
    match_urls = set()  # Using set to avoid duplicates
    
    for series_url in urls_to_check:
        result = await crawler.arun(url=series_url, config=run_config)
        
        if result.success:
            # Filter links that contain both the series ID and end with full-scorecard
            new_urls = [
                link['href'] for link in result.links["internal"] 
                if series_id in link['href'] and 'full-scorecard' in link['href']
            ]
            match_urls.update(new_urls)  # Add new URLs to set
        else:
            print(f"Failed to fetch page {series_url}: {result.error_message}")
    
    # Convert set to list and sort based on match number in URL
    match_urls = list(match_urls)
    
    def get_match_number(url):
        # Handle playoff matches first
        url_lower = url.lower()
        if 'qualifier-1' in url_lower or 'qualifier1' in url_lower:
            return 71
        if 'eliminator' in url_lower:
            return 72
        if 'qualifier-2' in url_lower or 'qualifier2' in url_lower:
            return 73
        if 'final' in url_lower:
            return 74
            
        # Handle league matches
        try:
            parts = url.split('/')[-2].split('-')
            for i, part in enumerate(parts):
                if part == 'match':
                    match_num = parts[i-1].rstrip('thndrdst')  # Remove ordinal suffixes
                    return int(match_num)
        except:
            return float('inf')  # Put URLs with unexpected format at the end
        return float('inf')
    
    match_urls.sort(key=get_match_number)
    
    print(f"\nFound {len(match_urls)} match URLs in chronological order:")
    for url in match_urls:
        match_num = get_match_number(url)
        match_id = url.split('/')[-2]  # Get the match identifier from URL
        print(f"Match #{match_num}: {match_id}")
        print(f"URL: {url}\n")
    
    return match_urls

async def main():
    browser_config = BrowserConfig(verbose=True)
    
    md_generator = DefaultMarkdownGenerator(
        options={
            "ignore_links": True,
            "escape_html": False,
            "body_width": 0
        }
    )
    
    run_config = CrawlerRunConfig(
        exclude_external_links=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=md_generator,
        cache_mode=CacheMode.DISABLED  # Disable cache to get fresh data
    )

    async with AsyncWebCrawler(config=browser_config) as crawler:
        print("Fetching match URLs from series pages...")
        match_urls = await get_series_matches(crawler, run_config)
        
        if not match_urls:
            print("No match URLs found. Exiting...")
            return


if __name__ == "__main__":
    asyncio.run(main())

