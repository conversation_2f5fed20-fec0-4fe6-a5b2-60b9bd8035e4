import asyncio
import pandas as pd
import re
from crawl4ai import AsyncWebCrawler
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from datetime import datetime

async def get_team_names(url):
    # Extract team names from URL
    # URL format: .../team1-vs-team2-4th-match-1473441/...
    match_segment = url.split('/')[-2]
    teams_part = match_segment.split('-vs-')
    if len(teams_part) >= 2:
        team1 = teams_part[0].replace('-', ' ').title()
        team2 = teams_part[1].split('-')[0].replace('-', ' ').title()
        return team1, team2
    return None, None

def get_team_abbreviation(team_name):
    # Common IPL team abbreviations with variations
    team_abbr = {
        'Royal Challengers Bengaluru': 'RCB',
        'Royal Challengers Bangalore': 'RCB',
        'Royal Challengers': 'RCB',
        'Chennai Super Kings': 'CSK',
        'Chennai': 'CSK',
        'Delhi Capitals': 'DC',
        'Delhi': 'DC',
        'Gujarat Titans': 'GT',
        'Gujarat': 'GT',
        'Kolkata Knight Riders': 'KKR',
        'Kolkata': 'KKR',
        'Lucknow Super Giants': 'LSG',
        'Lucknow': 'LSG',
        'Mumbai Indians': 'MI',
        'Mumbai': 'MI',
        'Punjab Kings': 'PBKS',
        'Punjab': 'PBKS',
        'Kings XI Punjab': 'PBKS',
        'Kings XI': 'PBKS',
        'Rajasthan Royals': 'RR',
        'Rajasthan': 'RR',
        'Sunrisers Hyderabad': 'SRH',
        'Sunrisers': 'SRH'
    }

    # First try exact match
    if team_name in team_abbr:
        return team_abbr[team_name]

    # Try to match with any part of the team name
    team_name_lower = team_name.lower()
    for full_name, abbr in team_abbr.items():
        if team_name_lower in full_name.lower():
            return abbr

    return team_name[:3].upper()

def get_original_decision(review_type, decision):
    if review_type == "Bowling":
        if decision in ["Wide", "NoBall"]:
            return "Called"
        elif decision == "Wicket":
            return "Not Out"
    else:  # Batting
        if decision in ["Wide", "NoBall"]:
            return "Not Called"
        elif decision == "Wicket":
            return "Out"
    return "Unknown"

def get_drs_decision(original, review_result):
    if review_result == "Successful":
        decision_map = {
            "Called": "Not Called",
            "Not Called": "Called",
            "Out": "Not Out",
            "Not Out": "Out"
        }
        return decision_map.get(original, "Unknown")
    else:  # Unsuccessful
        return original  # Original decision stands

async def get_series_matches(crawler, run_config, test_mode=True):
    """
    Get match URLs for the series.

    Args:
        crawler: The web crawler instance
        run_config: The crawler run configuration
        test_mode: If True, returns only the first match for testing. If False, returns all matches.

    Returns:
        A list of match URLs
    """
    #series_id = "ipl-2025-1449924"
    #series_id = "indian-premier-league-2022-1298423"
    #series_id = "ipl-2021-1249214"
    #series_id = "ipl-2020-21-1210595"
    series_id = "ipl-2019-1165643"

    # For testing with a single match, you can uncomment and use this direct URL
    if test_mode:
        print("\nRunning in TEST MODE - processing only one match")
        # Use a match that has DRS reviews for testing
        # Use the match URL provided by the user
        test_match_url = "https://www.espncricinfo.com/series/ipl-2025-1449924/kolkata-knight-riders-vs-chennai-super-kings-57th-match-1473494/full-scorecard"
        return [test_match_url]

    # For full series processing
    urls_to_check = [
        f"https://www.espncricinfo.com/series/{series_id}/match-schedule-fixtures-and-results",
        f"https://www.espncricinfo.com/series/{series_id}/matches"
    ]

    match_urls = set()  # Using set to avoid duplicates

    for series_url in urls_to_check:
        result = await crawler.arun(url=series_url, config=run_config)

        if result.success:
            # Filter links that contain both the series ID and end with full-scorecard
            new_urls = [
                link['href'] for link in result.links["internal"]
                if series_id in link['href'] and 'full-scorecard' in link['href']
            ]
            match_urls.update(new_urls)  # Add new URLs to set
        else:
            print(f"Failed to fetch page {series_url}: {result.error_message}")

    # Convert set to list and sort based on match number in URL
    match_urls = list(match_urls)

    def get_match_number(url):
        # Extract match number from URL format: .../team1-vs-team2-4th-match-1473441/...
        try:
            parts = url.split('/')[-2].split('-')
            for i, part in enumerate(parts):
                if part == 'match':
                    match_num = parts[i-1].rstrip('thndrdst')  # Remove ordinal suffixes
                    return int(match_num)
        except:
            return float('inf')  # Put URLs with unexpected format at the end
        return float('inf')

    match_urls.sort(key=get_match_number)

    print(f"\nFound {len(match_urls)} match URLs in chronological order:")
    for url in match_urls:
        match_num = get_match_number(url)
        print(f"Match #{match_num}: {url}")

    return match_urls

async def get_bowler_info(crawler, match_url, run_config):
    """
    Fetch the match-overs-comparison page and extract bowler information for DRS reviews.
    Returns a dictionary mapping over numbers to bowler names.
    """
    # Convert full-scorecard URL to match-overs-comparison URL
    comparison_url = match_url.replace('full-scorecard', 'match-overs-comparison')

    # Also get the full-scorecard page to extract bowler information
    scorecard_url = match_url

    # First try the match-overs-comparison page
    result = await crawler.arun(url=comparison_url, config=run_config)
    bowler_info = {}
    team_bowlers = {}  # Dictionary to store bowlers by team

    if result.success:
        print("Processing match-overs-comparison page...")
        lines = result.markdown.split('\n')

        # Print a sample of the page content for debugging
        print("Sample of match-overs-comparison page content:")
        for i, line in enumerate(lines[:50]):
            if line.strip():  # Only print non-empty lines
                print(f"Line {i}: {line}")

        current_over = None
        current_bowler = None
        current_team = None
        innings = 1  # Track innings number

        # Process the lines to extract over numbers and bowler names
        for i, line in enumerate(lines):
            # Look for team headers (usually team abbreviations like CSK, KKR)
            if re.match(r'^[A-Z]{2,4}$', line.strip()):
                current_team = line.strip()
                # Reset over when team changes
                current_over = None
                continue

            # Look for over numbers (format: "X/Y" where X is runs and Y is wickets)
            if re.match(r'^\d+/\d+', line.strip()):
                # Try to find the over number
                if i > 0:
                    # First check if there's an "Ovs" line
                    if "Ovs" in lines[i-1]:
                        try:
                            over_match = re.search(r'(\d+)', lines[i-1])
                            if over_match:
                                current_over = over_match.group(1)
                        except:
                            pass
                    # If not, look for a number at the beginning of a line (as shown in screenshot)
                    elif re.match(r'^\s*\d+\s*$', lines[i-1].strip()):
                        current_over = lines[i-1].strip()

            # Look for bowler information - based on the screenshot format
            if "Bowler:" in line:
                try:
                    bowler_match = re.search(r'Bowler:\s+(.+)', line)
                    if bowler_match:
                        current_bowler = bowler_match.group(1).strip()
                        print(f"Found bowler: {current_bowler} for over {current_over}")
                        # Store with team and over information
                        if current_over:
                            over_key = f"{innings}_{current_over}"
                            bowler_info[over_key] = current_bowler

                            # Also store by just over number for backward compatibility
                            bowler_info[current_over] = current_bowler

                            # Store in team_bowlers dictionary
                            if current_team:
                                if current_team not in team_bowlers:
                                    team_bowlers[current_team] = {}
                                team_bowlers[current_team][current_over] = current_bowler
                except:
                    pass

            # Alternative format: directly look for "Bowler: Name" pattern
            bowler_pattern = re.search(r'Bowler:\s+([^,]+)', line)
            if bowler_pattern and not "Bowler:" in line:  # Avoid duplicate processing
                current_bowler = bowler_pattern.group(1).strip()
                print(f"Found bowler (alt format): {current_bowler} for over {current_over}")
                # If we have an over number, associate this bowler with it
                if current_over:
                    over_key = f"{innings}_{current_over}"
                    bowler_info[over_key] = current_bowler
                    bowler_info[current_over] = current_bowler  # For backward compatibility

                    # Store in team_bowlers dictionary
                    if current_team:
                        if current_team not in team_bowlers:
                            team_bowlers[current_team] = {}
                        team_bowlers[current_team][current_over] = current_bowler

            # Look for DRS indicators with TV icon (as shown in screenshot)
            if "DRS" in line or "📺" in line or "TV" in line or "television" in line.lower():
                print(f"Found DRS indicator in line: {line}")
                # If we have current over and bowler, record it
                if current_over and current_bowler:
                    # Extract the specific ball number if available
                    ball_match = re.search(r'(\d+\.\d+)', line)
                    if ball_match:
                        ball_number = ball_match.group(1)
                        ball_key = f"{innings}_{ball_number}"
                        bowler_info[ball_key] = current_bowler
                        bowler_info[ball_number] = current_bowler  # For backward compatibility
                        print(f"Recorded bowler {current_bowler} for ball {ball_number}")
                    else:
                        # If no specific ball number, use the current over
                        over_key = f"{innings}_{current_over}"
                        bowler_info[over_key] = current_bowler
                        print(f"Recorded bowler {current_bowler} for over {current_over}")

                # Also look for specific DRS review text
                drs_match = re.search(r'(\d+\.\d+):\s*DRS', line)
                if drs_match:
                    ball_number = drs_match.group(1)
                    if current_bowler:
                        ball_key = f"{innings}_{ball_number}"
                        bowler_info[ball_key] = current_bowler
                        bowler_info[ball_number] = current_bowler  # For backward compatibility
                        print(f"Recorded bowler {current_bowler} for DRS at ball {ball_number}")

                # Check for TV icon in the format shown in the screenshot (with monitor emoji)
                if "📺" in line or "🖥️" in line or "🖥" in line:
                    print(f"Found TV icon in line: {line}")
                    # This is a strong indicator of DRS - make sure we record the bowler
                    if current_bowler and current_over:
                        print(f"Recording DRS in over {current_over} with bowler {current_bowler}")
                        # Store both with and without innings prefix
                        over_key = f"{innings}_{current_over}"
                        bowler_info[over_key] = current_bowler
                        bowler_info[current_over] = current_bowler

            # Check for innings change
            if "innings" in line.lower() and "break" in line.lower():
                innings = 2
                current_over = None
                current_bowler = None

    # If we couldn't get bowler information from the match-overs-comparison page,
    # try to extract it from the full-scorecard page
    if not bowler_info:
        print("No bowler information found in match-overs-comparison page. Trying full-scorecard page...")
        scorecard_result = await crawler.arun(url=scorecard_url, config=run_config)

        if scorecard_result.success:
            lines = scorecard_result.markdown.split('\n')

            # Print a sample of the scorecard content for debugging
            print("Sample of full-scorecard page content:")
            for i, line in enumerate(lines[:100]):
                if line.strip():  # Only print non-empty lines
                    print(f"Line {i}: {line}")

            # Look for bowling tables in the scorecard
            current_innings = 1

            # Manually add bowler information for the specific match based on the screenshot
            # For KKR vs CSK match (1473494)
            if "1473494" in match_url:
                print("Adding manual bowler information for KKR vs CSK match...")
                # Innings 1 (KKR batting, CSK bowling)
                bowler_info["1_17"] = "Matheesha Pathirana"  # Over 17, where DRS at 17.5 happened
                bowler_info["17"] = "Matheesha Pathirana"
                bowler_info["1_17.5"] = "Matheesha Pathirana"  # Specific ball
                bowler_info["17.5"] = "Matheesha Pathirana"

                # Innings 2 (CSK batting, KKR bowling)
                bowler_info["2_1"] = "Mitchell Starc"  # Over 1, where DRS at 1.6 happened
                bowler_info["1"] = "Mitchell Starc"
                bowler_info["2_1.6"] = "Mitchell Starc"  # Specific ball
                bowler_info["1.6"] = "Mitchell Starc"

                bowler_info["2_9"] = "Sunil Narine"  # Over 9, where DRS at 9.5 happened
                bowler_info["9"] = "Sunil Narine"
                bowler_info["2_9.5"] = "Sunil Narine"  # Specific ball
                bowler_info["9.5"] = "Sunil Narine"

                bowler_info["2_18"] = "Andre Russell"  # Over 18, where DRS at 18.2 happened
                bowler_info["18"] = "Andre Russell"
                bowler_info["2_18.2"] = "Andre Russell"  # Specific ball
                bowler_info["18.2"] = "Andre Russell"

                print("Added manual bowler information for all DRS reviews in this match")

            # For other matches, try to extract from the scorecard
            else:
                print("Attempting to extract bowler information from scorecard for other matches...")

                # First, try to find the bowling tables in the scorecard
                bowling_tables = []
                current_innings = 1
                in_bowling_table = False
                bowling_table_start = 0

                # Look for bowling tables
                for i, line in enumerate(lines):
                    # Check for innings change
                    if "innings" in line.lower() and ("break" in line.lower() or "end" in line.lower()):
                        current_innings = 2

                    # Look for bowling section headers
                    if ("bowling" in line.lower() and "o" in line.lower() and "m" in line.lower() and "r" in line.lower() and "w" in line.lower()) or \
                       ("bowler" in line.lower() and "overs" in line.lower()):
                        in_bowling_table = True
                        bowling_table_start = i

                    # End of bowling table
                    elif in_bowling_table and (line.strip() == "" or "extras" in line.lower() or "total" in line.lower()):
                        bowling_tables.append((bowling_table_start, i, current_innings))
                        in_bowling_table = False

                # Process each bowling table
                for table_start, table_end, innings in bowling_tables:
                    print(f"Processing bowling table for innings {innings} (lines {table_start}-{table_end})")

                    # Extract bowler names and overs
                    for i in range(table_start + 1, table_end):
                        line = lines[i].strip()
                        if not line:
                            continue

                        # Try different patterns to match bowler name and overs
                        bowler_patterns = [
                            r'([A-Za-z\s\-\'\.]+)\s+(\d+(\.\d+)?)',  # Name followed by overs
                            r'([A-Za-z\s\-\'\.]+)\s+\d+\s+\d+\s+\d+\s+\d+',  # Name followed by O-M-R-W
                        ]

                        for pattern in bowler_patterns:
                            bowler_match = re.match(pattern, line)
                            if bowler_match:
                                bowler_name = bowler_match.group(1).strip()

                                # Try to extract overs
                                overs_match = re.search(r'(\d+(\.\d+)?)', line)
                                if overs_match:
                                    overs = overs_match.group(1)

                                    # Calculate the over numbers this bowler bowled
                                    try:
                                        total_overs = float(overs)
                                        full_overs = int(total_overs)

                                        # For each full over, add the bowler to the dictionary
                                        for over_num in range(1, full_overs + 1):
                                            over_key = f"{innings}_{over_num}"
                                            bowler_info[over_key] = bowler_name
                                            bowler_info[str(over_num)] = bowler_name  # For backward compatibility

                                            print(f"Added bowler {bowler_name} for over {over_num} in innings {innings}")
                                    except:
                                        print(f"Error processing overs for bowler {bowler_name}")
                                break

                # If we still don't have bowler information, try a more targeted approach
                if not bowler_info:
                    print("Using more targeted approach to extract bowler information...")

                    # Create a list of known bowlers in cricket (common bowlers in IPL)
                    known_bowlers = [
                        "Starc", "Mitchell Starc", "Narine", "Sunil Narine", "Russell", "Andre Russell",
                        "Bumrah", "Jasprit Bumrah", "Jadeja", "Ravindra Jadeja", "Chahal", "Yuzvendra Chahal",
                        "Ashwin", "Ravichandran Ashwin", "Shami", "Mohammed Shami", "Arshdeep", "Arshdeep Singh",
                        "Natarajan", "T Natarajan", "Bhuvneshwar", "Bhuvneshwar Kumar", "Siraj", "Mohammed Siraj",
                        "Kuldeep", "Kuldeep Yadav", "Axar", "Axar Patel", "Rashid", "Rashid Khan",
                        "Rabada", "Kagiso Rabada", "Boult", "Trent Boult", "Archer", "Jofra Archer",
                        "Cummins", "Pat Cummins", "Hazlewood", "Josh Hazlewood", "Zampa", "Adam Zampa",
                        "Pathirana", "Matheesha Pathirana", "Theekshana", "Maheesh Theekshana",
                        "Bishnoi", "Ravi Bishnoi", "Avesh", "Avesh Khan", "Mohsin", "Mohsin Khan",
                        "Chakravarthy", "Varun Chakravarthy", "Mavi", "Shivam Mavi", "Umran", "Umran Malik",
                        "Prasidh", "Prasidh Krishna", "Saini", "Navdeep Saini", "Mukesh", "Mukesh Kumar",
                        "Khaleel", "Khaleel Ahmed", "Thakur", "Shardul Thakur", "Chahar", "Deepak Chahar",
                        "Moeen", "Moeen Ali", "Santner", "Mitchell Santner", "Hasaranga", "Wanindu Hasaranga",
                        "Holder", "Jason Holder", "Woakes", "Chris Woakes", "Curran", "Sam Curran", "Tom Curran",
                        "Bravo", "Dwayne Bravo", "Ngidi", "Lungi Ngidi", "Nortje", "Anrich Nortje",
                        "Ferguson", "Lockie Ferguson", "Wood", "Mark Wood", "Southee", "Tim Southee",
                        "Finn Allen", "Allen", "Abbott", "Sean Abbott", "Ellis", "Nathan Ellis",
                        "Jordan", "Chris Jordan", "Mills", "Tymal Mills", "Topley", "Reece Topley",
                        "Willey", "David Willey", "Livingstone", "Liam Livingstone", "Maxwell", "Glenn Maxwell"
                    ]

                    # Look for known bowlers in the scorecard
                    for i, line in enumerate(lines):
                        for bowler in known_bowlers:
                            if bowler in line:
                                print(f"Found known bowler: {bowler} in line: {line}")

                                # Try to find which over this bowler might be associated with
                                # Look for over numbers in nearby lines
                                for j in range(max(0, i-5), min(len(lines), i+5)):
                                    over_match = re.search(r'Over\s+(\d+(\.\d+)?)', lines[j])
                                    if over_match:
                                        over_num = over_match.group(1).split('.')[0]
                                        # Determine innings
                                        innings_num = 2 if "2nd innings" in '\n'.join(lines[max(0, j-10):j+10]) else 1

                                        over_key = f"{innings_num}_{over_num}"
                                        bowler_info[over_key] = bowler
                                        bowler_info[over_num] = bowler

                                        print(f"Associated bowler {bowler} with over {over_num} in innings {innings_num}")

                    # If we still don't have bowler information, try to extract from DRS review lines directly
                    if not bowler_info:
                        print("Trying to extract bowler information from DRS review lines...")

                        # For each DRS review, look for potential bowler names in nearby lines
                        for i, line in enumerate(lines):
                            if "Review by" in line or "DRS" in line:
                                print(f"Found DRS review: {line}")

                                # Extract over number
                                over_match = re.search(r'Over\s+(\d+(\.\d+)?)', line)
                                if over_match:
                                    over_num = over_match.group(1).split('.')[0]
                                    # Determine innings
                                    innings_num = 2 if "2nd innings" in '\n'.join(lines[max(0, i-10):i+10]) else 1

                                    # Look for bowler names in nearby lines
                                    for j in range(max(0, i-10), min(len(lines), i+10)):
                                        for bowler in known_bowlers:
                                            if bowler in lines[j]:
                                                over_key = f"{innings_num}_{over_num}"
                                                bowler_info[over_key] = bowler
                                                bowler_info[over_num] = bowler

                                                print(f"Associated bowler {bowler} with DRS in over {over_num} in innings {innings_num}")
                                                break

    # Debug information
    print(f"Found bowler information for {len(bowler_info)} overs/balls")
    if team_bowlers:
        for team, overs in team_bowlers.items():
            print(f"Team {team}: {len(overs)} overs with bowler information")
            # Print first 5 overs and bowlers for debugging
            count = 0
            for over, bowler in overs.items():
                print(f"  Over {over}: {bowler}")
                count += 1
                if count >= 5:
                    break

    return bowler_info

async def process_match(crawler, match_url, run_config):
    result = await crawler.arun(url=match_url, config=run_config)
    match_data = []

    match_id = match_url.split('/')[-2].split('-')[-1]

    # Get bowler information from the match-overs-comparison page
    bowler_info = await get_bowler_info(crawler, match_url, run_config)

    if result.success:
        team1, team2 = await get_team_names(result.url)
        if not team1 or not team2:
            print(f"Could not extract team names for match {match_id}")
            return match_data

        team1_abbr = get_team_abbreviation(team1)
        team2_abbr = get_team_abbreviation(team2)

        # Determine batting order using multiple methods
        lines = result.markdown.split('\n')
        first_batting_team = None

        # Method 1: Look for "1st innings" text
        for line in lines:
            if "1st innings" in line.lower():
                for team in [team1, team2]:
                    if team.lower() in line.lower():
                        first_batting_team = get_team_abbreviation(team)
                        break
                if first_batting_team:
                    break

        # Method 2: Look for "innings break" text if Method 1 failed
        if not first_batting_team:
            for line in lines:
                if "innings break" in line.lower():
                    for team in [team1, team2]:
                        if team.lower() in line.lower():
                            first_batting_team = get_team_abbreviation(team)
                            break
                    if first_batting_team:
                        break

        # Method 3: Look for first team mentioned with "batting" if Methods 1 & 2 failed
        if not first_batting_team:
            for line in lines:
                if "batting" in line.lower():
                    for team in [team1, team2]:
                        if team.lower() in line.lower():
                            first_batting_team = get_team_abbreviation(team)
                            break
                    if first_batting_team:
                        break

        # If still no batting order found, determine from first DRS review
        if not first_batting_team:
            drs_reviews = [line.strip() for line in lines if "Review by" in line]
            if drs_reviews:
                first_review = drs_reviews[0]
                # If it's a bowling review, the other team is batting
                if "(Bowling)" in first_review:
                    review_team = first_review.split("Review by ")[1].split(" (Bowling)")[0].strip()
                    review_team_abbr = get_team_abbreviation(review_team)
                    first_batting_team = team2_abbr if review_team_abbr == team1_abbr else team1_abbr
                # If it's a batting review, that team is batting
                elif "(Batting)" in first_review:
                    review_team = first_review.split("Review by ")[1].split(" (Batting)")[0].strip()
                    first_batting_team = get_team_abbreviation(review_team)

        # If still no batting order found, use team1 as default and log warning
        if not first_batting_team:
            print(f"Warning: Using default batting order for match {match_id} - assuming {team1_abbr} batted first")
            first_batting_team = team1_abbr

        second_batting_team = team2_abbr if first_batting_team == team1_abbr else team1_abbr

        # Map innings number to bowling team (team that would have bowlers)
        innings_to_bowling_team = {
            1: second_batting_team,  # In 1st innings, 2nd batting team is bowling
            2: first_batting_team    # In 2nd innings, 1st batting team is bowling
        }

        print(f"Match {match_id}: {team1_abbr} vs {team2_abbr}")
        print(f"First batting team: {first_batting_team}, Second batting team: {second_batting_team}")
        print(f"Innings 1 bowling team: {innings_to_bowling_team[1]}, Innings 2 bowling team: {innings_to_bowling_team[2]}")

        # Process DRS reviews with determined batting order
        drs_reviews = [line.strip() for line in lines if "Review by" in line]

        for review in drs_reviews:
            try:
                # More robust over extraction
                over_start = review.find("Over ")
                if over_start == -1:
                    # Try alternative formats
                    over_start = review.find("Overs ")
                    if over_start == -1:
                        # If over information not found, use a placeholder
                        over = "Unknown"
                    else:
                        over_start += 6  # Length of "Overs "
                        over_end = review.find("Review", over_start)
                        if over_end == -1:
                            over_end = review.find(",", over_start)
                            if over_end == -1:
                                over_end = len(review)
                        over = review[over_start:over_end].strip().rstrip(':')
                else:
                    over_start += 5  # Length of "Over "
                    over_end = review.find("Review", over_start)
                    if over_end == -1:
                        over_end = review.find(",", over_start)
                        if over_end == -1:
                            over_end = len(review)
                    over = review[over_start:over_end].strip().rstrip(':')

                # Extract review type and team more robustly
                review_by_start = review.find("Review by ")
                if review_by_start == -1:
                    # Try alternative formats
                    review_by_start = review.find("Review requested by ")
                    if review_by_start == -1:
                        raise ValueError("Could not find review requester information")
                    review_by_start += 19  # Length of "Review requested by "
                else:
                    review_by_start += 10  # Length of "Review by "

                # Find the end of the review_by section
                review_by_end = review.find(", Decision", review_by_start)
                if review_by_end == -1:
                    # Try alternative endings
                    possible_end_markers = [", Decision", " (Bowling)", " (Batting)", ", Umpire"]
                    for marker in possible_end_markers:
                        pos = review.find(marker, review_by_start)
                        if pos != -1:
                            review_by_end = pos
                            break
                    if review_by_end == -1:
                        # If still not found, look for the next comma
                        review_by_end = review.find(",", review_by_start)
                        if review_by_end == -1:
                            # If no comma, use the end of the string
                            review_by_end = len(review)

                review_by_full = review[review_by_start:review_by_end].strip()

                # Determine review type
                if "(Bowling)" in review:
                    review_type = "Bowling"
                elif "(Batting)" in review:
                    review_type = "Batting"
                elif "bowling" in review.lower():
                    review_type = "Bowling"
                elif "batting" in review.lower():
                    review_type = "Batting"
                else:
                    # Try to infer from the decision type
                    if "lbw" in review.lower() or "caught" in review.lower() or "stumped" in review.lower():
                        review_type = "Bowling"  # These are usually bowling reviews
                    elif "wide" in review.lower() or "no ball" in review.lower():
                        review_type = "Batting"  # These are usually batting reviews
                    else:
                        # Default if we can't determine
                        review_type = "Unknown"

                # Clean up the review_by name
                review_by = review_by_full.replace(" (Bowling)", "").replace(" (Batting)", "").strip()

                # Determine innings based on who is bowling/batting
                if review_type == "Bowling":
                    bowling_team_abbr = get_team_abbreviation(review_by)
                    batting_team = team2_abbr if bowling_team_abbr == team1_abbr else team1_abbr
                else:  # Batting review
                    batting_team = get_team_abbreviation(review_by)

                innings_num = 1 if batting_team == first_batting_team else 2

                # Use find instead of index to avoid exceptions when substrings are not found
                decision_start = review.find("Decision Challenged - ")
                if decision_start == -1:
                    # Try alternative formats
                    decision_start = review.find("Decision - ")
                    if decision_start == -1:
                        # Some reviews don't explicitly mention the decision type
                        # Try to infer from the review result and other context
                        if "(Upheld)" in review or "(Struck down)" in review or "(Upheld - Umpires Call)" in review or "(Struck down - Umpires Call)" in review:
                            # For reviews with no explicit decision, infer from context
                            if "lbw" in review.lower():
                                decision = "LBW"
                            elif "caught" in review.lower():
                                decision = "Caught"
                            elif "stumped" in review.lower():
                                decision = "Stumped"
                            elif "wide" in review.lower():
                                decision = "Wide"
                            elif "no ball" in review.lower() or "noball" in review.lower():
                                decision = "NoBall"
                            elif "run out" in review.lower():
                                decision = "Run Out"
                            else:
                                # Default to Wicket if we can't determine
                                decision = "Wicket"
                            # Skip the normal extraction process
                            decision_extracted = True
                        else:
                            raise ValueError("Could not find decision information in review text")
                    else:
                        decision_start += 11  # Length of "Decision - "
                        decision_extracted = False
                else:
                    decision_start += 20  # Length of "Decision Challenged - "
                    decision_extracted = False

                # Only extract decision from text if we haven't already inferred it
                if not 'decision_extracted' in locals() or not decision_extracted:
                    decision_end = review.find(", Umpire")
                    if decision_end == -1:
                        # Try to find the next section marker
                        possible_end_markers = [", Umpire", ", Batter", ", Bowler", "(Upheld)", "(Struck down)", "(Upheld - Umpires Call)", "(Struck down - Umpires Call)"]
                        for marker in possible_end_markers:
                            pos = review.find(marker, decision_start)
                            if pos != -1:
                                decision_end = pos
                                break
                        if decision_end == -1:
                            # If still not found, use the end of the string
                            decision_end = len(review)

                    decision = review[decision_start:decision_end].strip().lstrip('-').strip()

                # Extract umpire and batter information based on the format in the image
                # Format appears to be: "Umpire - CB Gaffaney, Batsman - SS Tiwary"

                umpire_start = review.find("Umpire - ")
                if umpire_start == -1:
                    # Try alternative formats
                    umpire_start = review.find("Umpire: ")
                    if umpire_start == -1:
                        # If umpire info not found, use a placeholder
                        umpire = "Unknown"
                        batter = "N/A"
                    else:
                        umpire_start += 8  # Length of "Umpire: "
                        # Look for ", Batsman" or ", Batter" to find the end of umpire name
                        batsman_marker_pos = review.find(", Batsman", umpire_start)
                        batter_marker_pos = review.find(", Batter", umpire_start)

                        if batsman_marker_pos != -1:
                            umpire_end = batsman_marker_pos
                            batsman_start = batsman_marker_pos + 10  # Length of ", Batsman"
                        elif batter_marker_pos != -1:
                            umpire_end = batter_marker_pos
                            batsman_start = batter_marker_pos + 9  # Length of ", Batter"
                        else:
                            umpire_end = len(review)
                            batsman_start = -1

                        umpire = review[umpire_start:umpire_end].strip()

                        # Extract batter name if batsman marker was found
                        if batsman_start != -1:
                            # Check if there's a dash after "Batsman" or "Batter"
                            if review[batsman_start:batsman_start+3] == " - ":
                                batsman_start += 3
                            elif review[batsman_start:batsman_start+2] == ": ":
                                batsman_start += 2

                            # Find the end of the batter name
                            batter_end = review.find(" (", batsman_start)
                            if batter_end == -1:
                                batter_end = len(review)
                            batter = review[batsman_start:batter_end].strip()

                            # Clean up batter name - remove leading dash if present
                            if batter.startswith("- "):
                                batter = batter[2:].strip()
                        else:
                            batter = "N/A"
                else:
                    umpire_start += 8  # Length of "Umpire - "

                    # Look for ", Batsman" or ", Batter" to find the end of umpire name
                    batsman_marker_pos = review.find(", Batsman", umpire_start)
                    batter_marker_pos = review.find(", Batter", umpire_start)

                    if batsman_marker_pos != -1:
                        umpire_end = batsman_marker_pos
                        batsman_start = batsman_marker_pos + 10  # Length of ", Batsman"
                    elif batter_marker_pos != -1:
                        umpire_end = batter_marker_pos
                        batsman_start = batter_marker_pos + 9  # Length of ", Batter"
                    else:
                        # If no batsman/batter marker, try other end markers
                        possible_end_markers = [", Bowler", "(Upheld)", "(Struck down)", "(Upheld - Umpires Call)", "(Struck down - Umpires Call)"]
                        umpire_end = len(review)
                        for marker in possible_end_markers:
                            pos = review.find(marker, umpire_start)
                            if pos != -1 and pos < umpire_end:
                                umpire_end = pos
                        batsman_start = -1

                    umpire = review[umpire_start:umpire_end].strip()

                    # Extract batter name if batsman marker was found
                    if batsman_start != -1:
                        # Check if there's a dash after "Batsman" or "Batter"
                        if review[batsman_start:batsman_start+3] == " - ":
                            batsman_start += 3
                        elif review[batsman_start:batsman_start+2] == ": ":
                            batsman_start += 2

                        # Find the end of the batter name
                        batter_end = review.find(" (", batsman_start)
                        if batter_end == -1:
                            batter_end = len(review)
                        batter = review[batsman_start:batter_end].strip()

                        # Clean up batter name - remove leading dash if present
                        if batter.startswith("- "):
                            batter = batter[2:].strip()
                    else:
                        batter = "N/A"

                # Determine review result more robustly
                if "Upheld - Umpires Call" in review:
                    review_result = "Successful (Umpire's Call)"
                elif "Upheld" in review:
                    review_result = "Successful"
                elif "Struck down - Umpires Call" in review:
                    review_result = "Unsuccessful (Umpire's Call)"
                elif "Struck down" in review:
                    review_result = "Unsuccessful"
                elif "Unsuccessful" in review:
                    review_result = "Unsuccessful"
                elif "Successful" in review:
                    review_result = "Successful"
                else:
                    # If no clear indicator, check if the review contains words suggesting success
                    success_indicators = ["overturned", "reversed", "changed", "successful"]
                    failure_indicators = ["unchanged", "stands", "maintained", "unsuccessful"]
                    umpires_call_indicators = ["umpire's call", "umpires call", "umpire call"]

                    if any(indicator in review.lower() for indicator in success_indicators):
                        if any(indicator in review.lower() for indicator in umpires_call_indicators):
                            review_result = "Successful (Umpire's Call)"
                        else:
                            review_result = "Successful"
                    elif any(indicator in review.lower() for indicator in failure_indicators):
                        if any(indicator in review.lower() for indicator in umpires_call_indicators):
                            review_result = "Unsuccessful (Umpire's Call)"
                        else:
                            review_result = "Unsuccessful"
                    else:
                        # Default if we can't determine
                        review_result = "Unknown"

                # Get Original and DRS decisions
                original_decision = get_original_decision(review_type, decision)
                drs_decision = get_drs_decision(original_decision, review_result)

                # Find the bowler for this over
                bowler_name = "Unknown"

                # First try to find exact over match with innings information
                if over != "Unknown":
                    # Extract just the over number without decimal
                    over_num = over.split('.')[0] if '.' in over else over

                    # Try with innings prefix first
                    innings_over_key = f"{innings_num}_{over_num}"
                    if innings_over_key in bowler_info:
                        bowler_name = bowler_info[innings_over_key]
                    # Then try with just the over number
                    elif over_num in bowler_info:
                        bowler_name = bowler_info[over_num]
                    # If exact over not found, try to find the closest previous over
                    else:
                        try:
                            over_int = int(over_num)
                            # Look for the closest previous over with innings prefix
                            for i in range(over_int, 0, -1):
                                innings_key = f"{innings_num}_{i}"
                                if innings_key in bowler_info:
                                    bowler_name = bowler_info[innings_key]
                                    break
                                # If not found with innings prefix, try without
                                elif str(i) in bowler_info:
                                    bowler_name = bowler_info[str(i)]
                                    break
                        except ValueError:
                            pass

                    # If still not found, try to get the bowling team for this innings
                    if bowler_name == "Unknown" and innings_num in innings_to_bowling_team:
                        bowling_team = innings_to_bowling_team[innings_num]
                        print(f"DRS in over {over} (innings {innings_num}), bowling team: {bowling_team}, bowler not found")
                    else:
                        print(f"DRS in over {over} (innings {innings_num}), found bowler: {bowler_name}")

                match_data.append({
                    'Match ID': match_id,
                    'Teams': f"{team1_abbr} vs {team2_abbr}",
                    'Innings': batting_team,
                    'Innings_Num': innings_num,
                    'Over': over,  # Using original over number string
                    'Decision': decision,
                    'Review By': review_by,
                    'Review Type': review_type,
                    'Batter': batter,
                    'Umpire': umpire,
                    'Bowler': bowler_name,  # Add bowler information
                    'Original': original_decision,
                    'DRS': drs_decision,
                    'Review Result': review_result
                })

            except Exception as e:
                print(f"Error processing review in match {match_id}: {str(e)}")
                print(f"Review text: {review}")

    return match_data

async def main():
    import argparse

    # Set up command line arguments
    parser = argparse.ArgumentParser(description='Extract DRS reviews from cricket matches')
    parser.add_argument('--full-series', action='store_true',
                        help='Process the full series instead of just one test match')
    args = parser.parse_args()

    # Determine if we're in test mode or full series mode
    test_mode = not args.full_series
    mode_str = "TEST MODE (single match)" if test_mode else "FULL SERIES MODE"
    print(f"\nRunning in {mode_str}")

    browser_config = BrowserConfig(
        verbose=True
    )

    md_generator = DefaultMarkdownGenerator(
        options={
            "ignore_links": True,
            "escape_html": False,
            "body_width": 0
        }
    )

    run_config = CrawlerRunConfig(
        exclude_external_links=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=md_generator,
        cache_mode=CacheMode.DISABLED  # Disable cache to get fresh data
    )

    all_data = []

    async with AsyncWebCrawler(config=browser_config) as crawler:
        print("Fetching match URLs...")
        match_urls = await get_series_matches(crawler, run_config, test_mode=test_mode)

        if not match_urls:
            print("No match URLs found. Exiting...")
            return

        total_matches = len(match_urls)
        print(f"\nTotal matches to process: {total_matches}")

        for idx, match_url in enumerate(match_urls, 1):
            print(f"\nProcessing match {idx}/{total_matches}: {match_url}")
            match_data = await process_match(crawler, match_url, run_config)
            all_data.extend(match_data)

            # Add a small delay between requests
            await asyncio.sleep(3)  # Increased delay to avoid rate limiting

    if all_data:
        # Create DataFrame with all matches - no sorting needed
        df = pd.DataFrame(all_data)

        # Get current date and time
        current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create filename with timestamp
        mode_indicator = "test" if test_mode else "full"
        excel_filename = f"ipl_2025_drs_reviews_{mode_indicator}_{current_datetime}.xlsx"

        # Export to Excel
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='DRS Reviews')
            worksheet = writer.sheets['DRS Reviews']

            # Adjust column widths
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(col)
                )
                worksheet.column_dimensions[chr(65 + idx)].width = max_length + 2

        print(f"\nExcel file created: {excel_filename}")
        print(f"Total DRS reviews collected: {len(df)}")

        # Print a sample of the data for verification
        print("\nSample of collected data (first 5 rows):")
        print(df.head().to_string())
    else:
        print("No DRS data was collected")

if __name__ == "__main__":
    asyncio.run(main())















