import asyncio
import logging
import aiohttp
from crawl4ai import <PERSON><PERSON><PERSON>, CrawlerRunConfig, LXMLWebScrapingStrategy, DefaultMarkdownGenerator, CacheMode
import re
from typing import Dict, Any
from bs4 import BeautifulSoup
import pandas as pd
import os

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def validate_url(url: str) -> bool:
    """Validate if the URL is accessible"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                return response.status == 200
    except Exception as e:
        logger.error(f"Error validating URL {url}: {str(e)}")
        return False

async def extract_match_data(crawler: Crawler, match_url: str) -> Dict[str, Any]:
    """
    Extract over-by-over data from a match-overs-comparison page.
    """
    logger.info(f"Starting data extraction from: {match_url}")

    # First validate the URL
    is_valid = await validate_url(match_url)
    if not is_valid:
        logger.error(f"URL {match_url} is not accessible")
        return None

    run_config = CrawlerRunConfig(
        exclude_external_links=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=DefaultMarkdownGenerator(
            options={
                "ignore_links": True,
                "escape_html": False,
                "body_width": 0
            }
        ),
        cache_mode=CacheMode.DISABLED
    )

    try:
        result = await crawler.arun(url=match_url, config=run_config)
        
        if not result.success:
            logger.error("Failed to fetch the page")
            return None

        logger.info("Successfully fetched the page. Processing content...")
        
        # Store both the markdown and raw HTML content for processing
        lines = result.markdown.split('\n')
        html_content = result.raw_content
        logger.info(f"Total lines in content: {len(lines)}")
        
        # Dictionary to store over-by-over information
        overs_data = {
            'innings1': {},
            'innings2': {}
        }
        
        current_innings = 1
        current_over = None
        current_bowler = None
        in_target_range = False
        line_count = 0

        # First parse all bowler information from the HTML content
        bowler_info = {}
        bowler_pattern = re.compile(r'(\d+)\s*\|\s*\d+/\d+\([^)]+\)\s*\*\*Bowler\s*:\*\*\s*([^•]+)')
        
        logger.info("Starting to parse bowler information...")
        for line in lines:
            match = bowler_pattern.search(line)
            if match:
                over_num = match.group(1)
                bowler = match.group(2).strip()
                bowler_info[over_num] = bowler
                logger.debug(f"Found bowler for over {over_num}: {bowler}")

        # Process each line
        logger.info("Processing over-by-over information...")
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Only process lines 213-234
            if line_count == 213:
                in_target_range = True
                logger.info("Starting to process target range (lines 213-234)")
                
            if in_target_range:
                logger.debug(f"Processing line {line_count}: {line}")
                
                # Check for innings break indicator
                if "innings break" in line.lower():
                    current_innings = 2
                    current_over = None
                    current_bowler = None
                    logger.info(f"Detected innings break, switching to innings {current_innings}")
                    continue
                
                # Extract over and score information
                over_match = re.match(r'^(\d+)\s*\|\s*(\d+/\d+)\(([^)]+)\)(?:\s*\*\*Bowler\s*:\*\*\s*([^•]+))?(?:\s*\|\s*(\d+/\d+)\(([^)]+)\))?', line)
                if over_match:
                    over_num = over_match.group(1)
                    innings1_score = over_match.group(2)
                    innings1_detail = over_match.group(3)
                    explicit_bowler = over_match.group(4)
                    innings2_score = over_match.group(5)
                    innings2_detail = over_match.group(6)

                    # Use explicit bowler if available, otherwise try from bowler_info
                    current_bowler = explicit_bowler.strip() if explicit_bowler else bowler_info.get(over_num)
                    
                    logger.debug(f"Found data for over {over_num}: Bowler={current_bowler}")
                    
                    # Store data for innings 1
                    overs_data['innings1'][over_num] = {
                        'bowler': current_bowler,
                        'score': innings1_score,
                        'detail': innings1_detail
                    }
                    
                    # Store data for innings 2 if available
                    if innings2_score:
                        overs_data['innings2'][over_num] = {
                            'bowler': bowler_info.get(f"{over_num}_2"),  # Try to get second innings bowler
                            'score': innings2_score,
                            'detail': innings2_detail
                        }
            
            line_count += 1
            
            if line_count > 234:
                in_target_range = False
                logger.info("Finished processing target range")
                break

        return overs_data
        
    except Exception as e:
        logger.error(f"Error during data extraction: {str(e)}")
        return None

def extract_match_data_from_excel(excel_path: str) -> Dict[str, Any]:
    """
    Extract match data from local Excel file.
    """
    try:
        logger.info(f"Reading Excel file: {excel_path}")
        df = pd.read_excel(excel_path)
        
        # Process the DataFrame to extract match and over information
        matches_data = {}
        
        for _, row in df.iterrows():
            match_id = str(row.get('Match ID', ''))
            if not match_id:
                continue
                
            # Extract over information if available
            over_info = {
                'innings1': {},
                'innings2': {}
            }
            
            # Try to get bowler information from relevant columns
            bowler = row.get('Bowler', 'Unknown')
            over_num = row.get('Over', '')
            
            if over_num:
                over_info['innings1'][str(over_num)] = {
                    'bowler': bowler,
                    'score': row.get('Score', 'N/A'),
                    'detail': f"Match {match_id}, Over {over_num}"
                }
            
            matches_data[match_id] = over_info
            
        return matches_data
        
    except Exception as e:
        logger.error(f"Error processing Excel file: {str(e)}", exc_info=True)
        return None

def find_latest_match_file(base_dir: str) -> str:
    """
    Find the most recent IPL 2025 match details Excel file.
    """
    try:
        # List all Excel files containing match details for 2025
        excel_files = []
        for root, _, files in os.walk(base_dir):
            for file in files:
                if "ipl_2025_match_details" in file.lower() and file.endswith(".xlsx"):
                    full_path = os.path.join(root, file)
                    excel_files.append(full_path)
        
        if not excel_files:
            logger.error("No IPL 2025 match details files found")
            return None
            
        # Get the most recent file based on modification time
        latest_file = max(excel_files, key=os.path.getmtime)
        logger.info(f"Found latest match details file: {latest_file}")
        return latest_file
        
    except Exception as e:
        logger.error(f"Error finding match files: {str(e)}", exc_info=True)
        return None

async def main():
    """Main function to test the extraction"""
    try:
        crawler = Crawler()
        
        # Test with multiple URLs to ensure we can access at least one
        test_urls = [
            "https://www.iplt20.com/match/2025/01",
            "https://www.iplt20.com/matches/2025/01",
            "https://www.iplt20.com/match/2025/match01",
            "https://www.iplt20.com/scores/2025/01"
        ]
        
        logger.info("Testing multiple URLs to find an accessible one...")
        
        for url in test_urls:
            logger.info(f"Trying URL: {url}")
            if await validate_url(url):
                logger.info(f"Successfully found accessible URL: {url}")
                match_data = await extract_match_data(crawler, url)
                if match_data:
                    print("\n=== MATCH DATA SUMMARY ===")
                    print("-" * 50)
                    for innings, overs in match_data.items():
                        print(f"\n{innings.upper()}")
                        print("-" * 30)
                        for over, data in sorted(overs.items(), key=lambda x: int(x[0])):
                            print(f"\nOver {over.rjust(2)}")
                            print(f"Bowler: {data.get('bowler', 'Unknown')}")
                            print(f"Score:  {data.get('score', 'N/A')}")
                            print(f"Detail: {data.get('detail', 'N/A')}")
                    break
            else:
                logger.warning(f"URL not accessible: {url}")
        else:
            logger.error("None of the test URLs were accessible")
            
        base_dir = r"c:\Users\<USER>\Desktop\crawl4ai"
        latest_file = find_latest_match_file(base_dir)
        
        if not latest_file:
            logger.error("Could not find any match detail files")
            return
            
        match_data = extract_match_data_from_excel(latest_file)
        
        if match_data:
            print("\n=== MATCH DATA SUMMARY FROM EXCEL ===")
            print("-" * 50)
            for match_id, data in match_data.items():
                print(f"\nMatch ID: {match_id}")
                print("-" * 30)
                for innings, overs in data.items():
                    if overs:
                        print(f"\n{innings.upper()}")
                        for over, over_data in sorted(overs.items(), key=lambda x: int(x[0]) if x[0].isdigit() else float('inf')):
                            print(f"\nOver {over.rjust(2)}")
                            print(f"Bowler: {over_data.get('bowler', 'Unknown')}")
                            print(f"Score:  {over_data.get('score', 'N/A')}")
                            print(f"Detail: {over_data.get('detail', 'N/A')}")
        else:
            logger.error("Failed to extract match data from Excel file")
            
    except Exception as e:
        logger.error(f"Error in main: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
