import asyncio
import re
import json
import pandas as pd
from crawl4ai import Async<PERSON>ebCrawler
from crawl4ai.markdown_generation_strategy import De<PERSON>ultMarkdownGenerator
from crawl4ai.async_configs import <PERSON><PERSON>erConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy

async def get_team_names(url):
    """
    Extract team names from URL
    URL format: .../team1-vs-team2-4th-match-1473441/...
    """
    match_segment = url.split('/')[-2]
    teams_part = match_segment.split('-vs-')
    if len(teams_part) >= 2:
        team1 = teams_part[0].replace('-', ' ').title()
        team2 = teams_part[1].split('-')[0].replace('-', ' ').title()
        return team1, team2
    return None, None

def get_team_abbreviation(team_name):
    """
    Get standard abbreviation for IPL team names
    """
    team_abbr = {
        'Royal Challengers Bangalore': 'RCB',
        'Chennai Super Kings': 'CSK',
        'Delhi Capitals': 'DC',
        'Gujarat Titans': 'GT',
        'Kolkata Knight Riders': 'KKR',
        'Lucknow Super Giants': 'LSG',
        'Mumbai Indians': 'MI',
        'Punjab Kings': 'PBKS',
        'Rajasthan Royals': 'RR',
        'Sunrisers Hyderabad': 'SRH'
    }
    return team_abbr.get(team_name, team_name[:3].upper())

async def extract_drs_data(match_url):
    """
    Extract DRS data from a match URL using AsyncWebCrawler
    """
    match_id = match_url.split('/')[-2].split('-')[-1]
    team1, team2 = await get_team_names(match_url)

    if not team1 or not team2:
        print(f"Could not extract team names for match {match_id}")
        return []

    team1_abbr = get_team_abbreviation(team1)
    team2_abbr = get_team_abbreviation(team2)

    print(f"Processing match: {team1_abbr} vs {team2_abbr} (ID: {match_id})")

    # Configure the browser and crawler
    browser_config = BrowserConfig(verbose=True)

    md_generator = DefaultMarkdownGenerator(
        options={
            "ignore_links": True,
            "escape_html": False,
            "body_width": 0
        }
    )

    run_config = CrawlerRunConfig(
        exclude_external_links=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=md_generator,
        cache_mode=CacheMode.DISABLED  # Disable cache to get fresh data
    )

    all_reviews = []

    try:
        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(url=match_url, config=run_config)

            if not result.success:
                print(f"Failed to fetch page: {result.error_message}")
                return []

            # Look for the JSON data in the page source
            # The HTML content might be in different attributes depending on the crawler version
            html_content = getattr(result, 'raw_content', None)
            if html_content is None:
                html_content = getattr(result, 'html', None)
            if html_content is None:
                # Try to get the content from the response attribute
                response = getattr(result, 'response', None)
                if response:
                    html_content = getattr(response, 'text', '')

            if not html_content:
                print("Could not extract HTML content from the page")
                return []

            # Print the first 500 characters of the HTML content for debugging
            print(f"HTML content preview: {html_content[:500]}...")

            # Try different patterns to find the JSON data
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__MATCH_DATA__\s*=\s*({.*?});',
                r'window\.__SCORECARD_DATA__\s*=\s*({.*?});',
                r'window\.__MATCH_INFO__\s*=\s*({.*?});',
                r'<script id="__NEXT_DATA__" type="application/json">(.*?)</script>'
            ]

            json_match = None
            for pattern in json_patterns:
                json_match = re.search(pattern, html_content, re.DOTALL)
                if json_match:
                    print(f"Found JSON data using pattern: {pattern}")
                    break

            if not json_match:
                print("Could not find JSON data in the page source")
                return []

            # Extract and parse the JSON data
            json_str = json_match.group(1)
            json_data = json.loads(json_str)

            # The structure might be different depending on which pattern matched
            # For __NEXT_DATA__ pattern, the structure is different
            if '__NEXT_DATA__' in html_content:
                # Print the keys at the top level for debugging
                print(f"Top level keys: {list(json_data.keys())}")

                # Navigate through the JSON structure to find the match data
                props = json_data.get('props', {})
                page_props = props.get('pageProps', {})

                # Print the keys in pageProps
                print(f"pageProps keys: {list(page_props.keys())}")

                # Check if we have match data
                if 'match' in page_props:
                    match = page_props.get('match', {})
                    print(f"match keys: {list(match.keys())}")

                    # Try to find innings data
                    if 'innings' in match:
                        innings_data = match.get('innings', [])
                        print(f"Found innings data with {len(innings_data)} innings")
                    else:
                        # Look for scorecard data
                        scorecard = match.get('scorecard', {})
                        if scorecard:
                            print(f"scorecard keys: {list(scorecard.keys())}")
                            innings_data = scorecard.get('innings', [])
                            print(f"Found innings data with {len(innings_data)} innings")
                        else:
                            # Try other paths
                            innings_data = []
                            print("No innings data found in match or scorecard")
                elif 'data' in page_props:
                    data = page_props.get('data', {})
                    print(f"data keys: {list(data.keys())}")

                    # Try to find innings data
                    innings_data = data.get('innings', [])
                    print(f"Found innings data with {len(innings_data)} innings")
                else:
                    # Try alternative paths
                    content = page_props.get('content', {})
                    if content:
                        print(f"content keys: {list(content.keys())}")

                    innings_data = content.get('innings', [])

                    if not innings_data:
                        match_data = page_props.get('matchData', {})
                        if match_data:
                            print(f"matchData keys: {list(match_data.keys())}")

                        innings_data = match_data.get('innings', [])

                    print(f"Found innings data with {len(innings_data)} innings")
            else:
                # Original structure for __INITIAL_STATE__ pattern
                innings_data = json_data.get('content', {}).get('innings', [])

            # Process each innings
            for innings_idx, innings in enumerate(innings_data, 1):
                # Get batting team for this innings
                batting_team = innings.get('team', {}).get('name', '')
                batting_team_abbr = get_team_abbreviation(batting_team)

                # Get bowling team for this innings
                bowling_team = team2 if batting_team == team1 else team1
                bowling_team_abbr = get_team_abbreviation(bowling_team)

                print(f"\nInnings {innings_idx}: {batting_team_abbr} batting, {bowling_team_abbr} bowling")

                # Get DRS reviews for this innings
                drs_reviews = innings.get('inningDRSReviews', [])
                print(f"Found {len(drs_reviews)} DRS reviews in this innings")

                for review_idx, review in enumerate(drs_reviews, 1):
                    try:
                        # Extract review details
                        decision_type = review.get('decisionChallengeType', 0)

                        # Map decision types to readable format
                        decision_type_map = {
                            1: "LBW",
                            2: "Caught",
                            3: "Stumped",
                            4: "Run Out",
                            5: "Hit Wicket",
                            6: "Obstructing the Field",
                            7: "Timed Out",
                            8: "No Ball",
                            9: "Wide",
                            10: "Boundary",
                            11: "Bump Ball"
                        }

                        decision_text = decision_type_map.get(decision_type, f"Unknown ({decision_type})")

                        # Get review team (batting or bowling)
                        review_team = review.get('reviewTeam', {})
                        review_team_name = review_team.get('name', '')
                        review_team_abbr = review_team.get('abbreviation', '')

                        # Determine if it's a batting or bowling review
                        is_batting_review = review_team_name == batting_team
                        review_type = "Batting" if is_batting_review else "Bowling"

                        # Get umpire information
                        umpire = review.get('umpire', {})
                        umpire_name = umpire.get('name', '')

                        # Get batsman information
                        batsman = review.get('batsman', {})
                        batsman_name = batsman.get('name', '')

                        # Get bowler information
                        bowler = review.get('bowler', {})
                        bowler_name = bowler.get('name', '')

                        # Get over information
                        over_num = review.get('overNumber', '')
                        ball_num = review.get('ballNumber', '')
                        over_ball = f"{over_num}.{ball_num}" if over_num and ball_num else "Unknown"

                        # Get DRS decision
                        drs_decision = review.get('drsDecision', '')
                        is_successful = drs_decision == 1
                        result = "Successful" if is_successful else "Unsuccessful"

                        # Create review data dictionary
                        review_data = {
                            'Match ID': match_id,
                            'Teams': f"{team1_abbr} vs {team2_abbr}",
                            'Innings': innings_idx,
                            'Batting Team': batting_team_abbr,
                            'Bowling Team': bowling_team_abbr,
                            'Over': over_ball,
                            'Decision Type': decision_text,
                            'Review By': review_team_abbr,
                            'Review Type': review_type,
                            'Batsman': batsman_name,
                            'Bowler': bowler_name,
                            'Umpire': umpire_name,
                            'Result': result
                        }

                        all_reviews.append(review_data)

                        # Print formatted output for each review
                        print(f"\nDRS Review #{review_idx}:")
                        print(f"Over: {over_ball}")
                        print(f"Decision: {decision_text}")
                        print(f"Review By: {review_team_abbr} ({review_type})")
                        print(f"Batsman: {batsman_name}")
                        print(f"Bowler: {bowler_name}")
                        print(f"Umpire: {umpire_name}")
                        print(f"Result: {result}")
                        print("-" * 50)

                    except Exception as e:
                        print(f"Error processing review {review_idx}: {str(e)}")
                        print(f"Review data: {review}")

    except Exception as e:
        print(f"Error processing match: {str(e)}")

    return all_reviews

async def main():
    # URL for testing
    match_url = "https://www.espncricinfo.com/series/ipl-2025-1449924/kolkata-knight-riders-vs-chennai-super-kings-57th-match-1473494/full-scorecard"

    # Extract DRS data
    all_reviews = await extract_drs_data(match_url)

    # Create DataFrame with all reviews
    if all_reviews:
        df = pd.DataFrame(all_reviews)

        # Export to Excel
        match_id = match_url.split('/')[-2].split('-')[-1]
        excel_filename = f"drs_reviews_{match_id}.xlsx"

        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='DRS Reviews')

            # Adjust column widths
            worksheet = writer.sheets['DRS Reviews']
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(col)
                )
                worksheet.column_dimensions[chr(65 + idx)].width = max_length + 2

        print(f"\nExcel file created: {excel_filename}")
        print(f"Total DRS reviews collected: {len(df)}")
    else:
        print("No DRS data was collected")

if __name__ == "__main__":
    asyncio.run(main())
