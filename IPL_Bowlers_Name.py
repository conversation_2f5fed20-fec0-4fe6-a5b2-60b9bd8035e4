import asyncio
import pandas as pd
from crawl4ai import AsyncWebCrawler
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from datetime import datetime

async def get_team_names(url):
    # Extract team names from URL
    # URL format: .../team1-vs-team2-4th-match-1473441/...
    match_segment = url.split('/')[-2]
    teams_part = match_segment.split('-vs-')
    if len(teams_part) >= 2:
        team1 = teams_part[0].replace('-', ' ').title()
        team2 = teams_part[1].split('-')[0].replace('-', ' ').title()
        return team1, team2
    return None, None

def get_team_abbreviation(team_name):
    # Common IPL team abbreviations with variations
    team_abbr = {
        'Royal Challengers Bengaluru': 'RCB',
        'Royal Challengers Bangalore': 'RCB',
        'Royal Challengers': 'RCB',
        'Chennai Super Kings': 'CSK',
        'Chennai': 'CSK',
        'Delhi Capitals': 'DC',
        'Delhi': 'DC',
        'Gujarat Titans': 'GT',
        'Gujarat': 'GT',
        'Kolkata Knight Riders': 'KKR',
        'Kolkata': 'KKR',
        'Lucknow Super Giants': 'LSG',
        'Lucknow': 'LSG',
        'Mumbai Indians': 'MI',
        'Mumbai': 'MI',
        'Punjab Kings': 'PBKS',
        'Punjab': 'PBKS',
        'Kings XI Punjab': 'PBKS',
        'Kings XI': 'PBKS',
        'Rajasthan Royals': 'RR',
        'Rajasthan': 'RR',
        'Sunrisers Hyderabad': 'SRH',
        'Sunrisers': 'SRH'
    }

    # First try exact match
    if team_name in team_abbr:
        return team_abbr[team_name]

    # Try to match with any part of the team name
    team_name_lower = team_name.lower()
    for full_name, abbr in team_abbr.items():
        if team_name_lower in full_name.lower():
            return abbr

    return team_name[:3].upper()

async def get_series_matches(crawler, run_config):
    #series_id = "ipl-2025-1449924"
    #series_id = "indian-premier-league-2022-1298423"
    #series_id = "ipl-2021-1249214"
    #series_id = "ipl-2020-21-1210595"
    series_id = "ipl-2019-1165643"
    urls_to_check = [
        f"https://www.espncricinfo.com/series/{series_id}/match-schedule-fixtures-and-results",
        f"https://www.espncricinfo.com/series/{series_id}/matches"
    ]

    match_urls = set()  # Using set to avoid duplicates

    for series_url in urls_to_check:
        result = await crawler.arun(url=series_url, config=run_config)

        if result.success:
            # Filter links that contain both the series ID and end with full-scorecard
            new_urls = [
                link['href'] for link in result.links["internal"]
                if series_id in link['href'] and 'full-scorecard' in link['href']
            ]
            match_urls.update(new_urls)  # Add new URLs to set
        else:
            print(f"Failed to fetch page {series_url}: {result.error_message}")

    # Convert set to list and sort based on match number in URL
    match_urls = list(match_urls)

    def get_match_number(url):
        # Extract match number from URL format: .../team1-vs-team2-4th-match-1473441/...
        try:
            parts = url.split('/')[-2].split('-')
            for i, part in enumerate(parts):
                if part == 'match':
                    match_num = parts[i-1].rstrip('thndrdst')  # Remove ordinal suffixes
                    return int(match_num)
        except:
            return float('inf')  # Put URLs with unexpected format at the end
        return float('inf')

    match_urls.sort(key=get_match_number)

    print(f"\nFound {len(match_urls)} match URLs in chronological order:")
    for url in match_urls:
        match_num = get_match_number(url)
        print(f"Match #{match_num}: {url}")

    return match_urls

async def process_match(crawler, match_url, run_config):
    result = await crawler.arun(url=match_url, config=run_config)
    match_data = []

    match_id = match_url.split('/')[-2].split('-')[-1]

    if result.success:
        team1, team2 = await get_team_names(result.url)
        if not team1 or not team2:
            print(f"Could not extract team names for match {match_id}")
            return match_data

        team1_abbr = get_team_abbreviation(team1)
        team2_abbr = get_team_abbreviation(team2)

        # Determine batting order using multiple methods
        lines = result.markdown.split('\n')
        first_batting_team = None
