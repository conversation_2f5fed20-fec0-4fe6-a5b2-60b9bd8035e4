import asyncio
import pandas as pd
from crawl4ai import AsyncWebCrawler
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from datetime import datetime

async def get_team_names(url):
    # Extract team names from URL
    # URL format: .../team1-vs-team2-4th-match-1473441/...
    match_segment = url.split('/')[-2]
    teams_part = match_segment.split('-vs-')
    if len(teams_part) >= 2:
        team1 = teams_part[0].replace('-', ' ').title()
        team2 = teams_part[1].split('-')[0].replace('-', ' ').title()
        return team1, team2
    return None, None

def get_team_abbreviation(team_name):
    # Common IPL team abbreviations with variations
    team_abbr = {
        'Royal Challengers Bengaluru': 'RCB',
        'Royal Challengers': 'RCB',
        'Chennai Super Kings': 'CSK',
        'Chennai': 'CSK',
        'Delhi Capitals': 'DC',
        'Delhi': 'DC',
        'Gujarat Titans': 'GT',
        'Gujarat': 'GT',
        'Kolkata Knight Riders': 'KKR',
        'Kolkata': 'KKR',
        'Lucknow Super Giants': 'LSG',
        'Lucknow': 'LSG',
        'Mumbai Indians': 'MI',
        'Mumbai': 'MI',
        'Punjab Kings': 'PBKS',
        'Punjab': 'PBKS',
        'Rajasthan Royals': 'RR',
        'Rajasthan': 'RR',
        'Sunrisers Hyderabad': 'SRH',
        'Sunrisers': 'SRH'
    }
    
    # First try exact match
    if team_name in team_abbr:
        return team_abbr[team_name]
    
    # Try to match with any part of the team name
    team_name_lower = team_name.lower()
    for full_name, abbr in team_abbr.items():
        if team_name_lower in full_name.lower():
            return abbr
    
    return team_name[:3].upper()

def get_original_decision(review_type, decision):
    if review_type == "Bowling":
        if decision in ["Wide", "NoBall"]:
            return "Called"
        elif decision == "Wicket":
            return "Not Out"
    else:  # Batting
        if decision in ["Wide", "NoBall"]:
            return "Not Called"
        elif decision == "Wicket":
            return "Out"
    return "Unknown"

def get_drs_decision(original, review_result):
    if review_result == "Successful":
        decision_map = {
            "Called": "Not Called",
            "Not Called": "Called",
            "Out": "Not Out",
            "Not Out": "Out"
        }
        return decision_map.get(original, "Unknown")
    else:  # Unsuccessful
        return original  # Original decision stands

async def get_series_matches(crawler, run_config):
    series_id = "indian-premier-league-2024-1410320"
    urls_to_check = [
        f"https://www.espncricinfo.com/series/{series_id}/match-schedule-fixtures-and-results",
        f"https://www.espncricinfo.com/series/{series_id}/matches"
    ]
    
    match_urls = set()  # Using set to avoid duplicates
    
    for series_url in urls_to_check:
        result = await crawler.arun(url=series_url, config=run_config)
        
        if result.success:
            # Filter links that contain both the series ID and end with full-scorecard
            new_urls = [
                link['href'] for link in result.links["internal"] 
                if series_id in link['href'] and 'full-scorecard' in link['href']
            ]
            match_urls.update(new_urls)  # Add new URLs to set
        else:
            print(f"Failed to fetch page {series_url}: {result.error_message}")
    
    # Convert set to list and sort based on match number in URL
    match_urls = list(match_urls)
    
    def get_match_number(url):
        # Handle playoff matches first
        url_lower = url.lower()
        if 'qualifier-1' in url_lower or 'qualifier1' in url_lower:
            return 71
        if 'eliminator' in url_lower:
            return 72
        if 'qualifier-2' in url_lower or 'qualifier2' in url_lower:
            return 73
        if 'final' in url_lower:
            return 74

        # Extract match number from URL format: .../team1-vs-team2-4th-match-1473441/...
        try:
            parts = url.split('/')[-2].split('-')
            for i, part in enumerate(parts):
                if part == 'match':
                    match_num = parts[i-1].rstrip('thndrdst')  # Remove ordinal suffixes
                    return int(match_num)
        except:
            return float('inf')  # Put URLs with unexpected format at the end
        return float('inf')
    
    match_urls.sort(key=get_match_number)
    
    print(f"\nFound {len(match_urls)} match URLs in chronological order:")
    for url in match_urls:
        match_num = get_match_number(url)
        print(f"Match #{match_num}: {url}")
    
    return match_urls

async def process_match(crawler, match_url, run_config):
    result = await crawler.arun(url=match_url, config=run_config)
    match_data = []
    
    match_id = match_url.split('/')[-2].split('-')[-1]
    
    if result.success:
        team1, team2 = await get_team_names(result.url)
        if not team1 or not team2:
            print(f"Could not extract team names for match {match_id}")
            return match_data

        team1_abbr = get_team_abbreviation(team1)
        team2_abbr = get_team_abbreviation(team2)
        
        # Determine batting order using multiple methods
        lines = result.markdown.split('\n')
        first_batting_team = None
        
        # Method 1: Look for "1st innings" text
        for line in lines:
            if "1st innings" in line.lower():
                for team in [team1, team2]:
                    if team.lower() in line.lower():
                        first_batting_team = get_team_abbreviation(team)
                        break
                if first_batting_team:
                    break
        
        # Method 2: Look for "innings break" text if Method 1 failed
        if not first_batting_team:
            for line in lines:
                if "innings break" in line.lower():
                    for team in [team1, team2]:
                        if team.lower() in line.lower():
                            first_batting_team = get_team_abbreviation(team)
                            break
                    if first_batting_team:
                        break
        
        # Method 3: Look for first team mentioned with "batting" if Methods 1 & 2 failed
        if not first_batting_team:
            for line in lines:
                if "batting" in line.lower():
                    for team in [team1, team2]:
                        if team.lower() in line.lower():
                            first_batting_team = get_team_abbreviation(team)
                            break
                    if first_batting_team:
                        break
        
        # If still no batting order found, determine from first DRS review
        if not first_batting_team:
            drs_reviews = [line.strip() for line in lines if "Review by" in line]
            if drs_reviews:
                first_review = drs_reviews[0]
                # If it's a bowling review, the other team is batting
                if "(Bowling)" in first_review:
                    review_team = first_review.split("Review by ")[1].split(" (Bowling)")[0].strip()
                    review_team_abbr = get_team_abbreviation(review_team)
                    first_batting_team = team2_abbr if review_team_abbr == team1_abbr else team1_abbr
                # If it's a batting review, that team is batting
                elif "(Batting)" in first_review:
                    review_team = first_review.split("Review by ")[1].split(" (Batting)")[0].strip()
                    first_batting_team = get_team_abbreviation(review_team)
        
        # If still no batting order found, use team1 as default and log warning
        if not first_batting_team:
            print(f"Warning: Using default batting order for match {match_id} - assuming {team1_abbr} batted first")
            first_batting_team = team1_abbr
            
        second_batting_team = team2_abbr if first_batting_team == team1_abbr else team1_abbr
        
        # Process DRS reviews with determined batting order
        drs_reviews = [line.strip() for line in lines if "Review by" in line]
        
        for review in drs_reviews:
            try:
                # Simplified over extraction
                over_text = review.split("Over ")[1]
                over = over_text.split("Review")[0].strip()  # Remove everything after and including "Review"
                over = over.rstrip(':')  # Remove trailing colon if present
                
                # Extract review type and team
                review_by_full = review.split("Review by ")[1].split(", Decision")[0].strip()
                review_type = "Bowling" if "(Bowling)" in review_by_full else "Batting"
                review_by = review_by_full.replace(" (Bowling)", "").replace(" (Batting)", "").strip()
                
                # Determine innings based on who is bowling/batting
                if review_type == "Bowling":
                    bowling_team_abbr = get_team_abbreviation(review_by)
                    batting_team = team2_abbr if bowling_team_abbr == team1_abbr else team1_abbr
                else:  # Batting review
                    batting_team = get_team_abbreviation(review_by)
                
                innings_num = 1 if batting_team == first_batting_team else 2
                
                decision_start = review.index("Decision Challenged - ") + 20
                decision_end = review.index(", Umpire")
                decision = review[decision_start:decision_end].strip().lstrip('-').strip()
                
                umpire_start = review.index("Umpire - ") + 8
                umpire_end = review.index(", Batter")
                umpire = review[umpire_start:umpire_end].strip()
                
                parts = review.split(", Batter - ")
                batter = parts[1].split(" (")[0].strip() if len(parts) > 1 else "N/A"
                
                review_result = "Successful" if "Upheld" in review else "Unsuccessful"
                
                # Get Original and DRS decisions
                original_decision = get_original_decision(review_type, decision)
                drs_decision = get_drs_decision(original_decision, review_result)
                
                match_data.append({
                    'Match ID': match_id,
                    'Teams': f"{team1_abbr} vs {team2_abbr}",
                    'Innings': batting_team,
                    'Innings_Num': innings_num,
                    'Over': over,  # Using original over number string
                    'Decision': decision,
                    'Review By': review_by,
                    'Review Type': review_type,
                    'Batter': batter,
                    'Umpire': umpire,
                    'Original': original_decision,
                    'DRS': drs_decision,
                    'Review Result': review_result
                })
                
            except Exception as e:
                print(f"Error processing review in match {match_id}: {str(e)}")
                print(f"Review text: {review}")
        
    return match_data

async def main():
    browser_config = BrowserConfig(
        verbose=True
    )
    
    md_generator = DefaultMarkdownGenerator(
        options={
            "ignore_links": True,
            "escape_html": False,
            "body_width": 0
        }
    )
    
    run_config = CrawlerRunConfig(
        exclude_external_links=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=md_generator,
        cache_mode=CacheMode.DISABLED  # Disable cache to get fresh data
    )

    all_data = []

    async with AsyncWebCrawler(config=browser_config) as crawler:
        print("Fetching match URLs from series pages...")
        match_urls = await get_series_matches(crawler, run_config)
        
        if not match_urls:
            print("No match URLs found. Exiting...")
            return
        
        total_matches = len(match_urls)
        print(f"\nTotal matches to process: {total_matches}")
        
        for idx, match_url in enumerate(match_urls, 1):
            print(f"\nProcessing match {idx}/{total_matches}: {match_url}")
            match_data = await process_match(crawler, match_url, run_config)
            all_data.extend(match_data)
            
            # Add a small delay between requests
            await asyncio.sleep(3)  # Increased delay to avoid rate limiting
    
    if all_data:
        # Create DataFrame with all matches - no sorting needed
        df = pd.DataFrame(all_data)
        
        # Get current date and time
        current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filename with timestamp
        excel_filename = f"ipl_2024_drs_reviews_{current_datetime}.xlsx"
        
        # Export to Excel
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='DRS Reviews')
            worksheet = writer.sheets['DRS Reviews']
            
            # Adjust column widths
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(col)
                )
                worksheet.column_dimensions[chr(65 + idx)].width = max_length + 2
        
        print(f"\nExcel file created: {excel_filename}")
        print(f"Total DRS reviews collected: {len(df)}")
    else:
        print("No DRS data was collected")

if __name__ == "__main__":
    asyncio.run(main())